'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import {
  Bold,
  Italic,
  Underline,
  List,
  ListOrdered,
  Image,
  AlignLeft,
  AlignCenter,
  AlignRight,
  Undo,
  Redo,
} from 'lucide-react';
import { appTheme } from '@/app/theme';
import ResizableImage from './ResizableImage';

interface TextEditorProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  uploadImage?: (file: File) => Promise<string>;
}

const EditorContainer = styled.div`
  border: 1px solid #d1d5db;
  border-radius: ${appTheme.borderRadius.md};
  background-color: ${appTheme.colors.background.main};
  overflow: hidden;
`;

const Toolbar = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px 12px;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  flex-wrap: wrap;
`;

const ToolbarButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background-color: ${props => props.$active ? '#e5e7eb' : 'transparent'};
  color: ${props => props.$active ? '#374151' : '#6b7280'};
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background-color: #e5e7eb;
    color: #374151;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  svg {
    width: 16px;
    height: 16px;
  }
`;

const ToolbarSeparator = styled.div`
  width: 1px;
  height: 24px;
  background-color: #e5e7eb;
  margin: 0 4px;
`;

const EditorContent = styled.div<{ $minHeight?: number; $maxHeight?: number }>`
  min-height: ${props => props.$minHeight || 150}px;
  max-height: ${props => props.$maxHeight || 400}px;
  overflow-y: auto;
  padding: 12px;
  font-family: inherit;
  font-size: ${appTheme.typography.fontSizes.sm};
  line-height: 1.5;
  color: ${appTheme.colors.text.primary};
  outline: none;
  
  &:empty:before {
    content: attr(data-placeholder);
    color: #9ca3af;
    pointer-events: none;
  }

  p {
    margin: 0 0 8px 0;
    
    &:last-child {
      margin-bottom: 0;
    }
  }

  ul, ol {
    margin: 8px 0;
    padding-left: 24px;
  }

  li {
    margin: 4px 0;
  }

  a {
    color: #3b82f6;
    text-decoration: underline;
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 8px 0;
  }

  strong {
    font-weight: 600;
  }

  em {
    font-style: italic;
  }

  u {
    text-decoration: underline;
  }

  .text-left {
    text-align: left;
  }

  .text-center {
    text-align: center;
  }

  .text-right {
    text-align: right;
  }
`;

const ImageUploadInput = styled.input`
  display: none;
`;

// Modal styles
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background: white;
  border-radius: 8px;
  padding: 24px;
  max-width: 90vw;
  max-height: 90vh;
  overflow: auto;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ModalTitle = styled.h3`
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #111827;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  padding: 4px;
  border-radius: 4px;

  &:hover {
    background: #f3f4f6;
    color: #111827;
  }
`;

interface ImageResizeModalProps {
  src: string;
  initialWidth: number;
  initialHeight: number;
  onResize: (width: number, height: number) => void;
  onRemove: () => void;
  onClose: () => void;
}

const ImageResizeModal: React.FC<ImageResizeModalProps> = ({
  src,
  initialWidth,
  initialHeight,
  onResize,
  onRemove,
  onClose
}) => {
  return (
    <ModalOverlay onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <ModalHeader>
          <ModalTitle>Resize Image</ModalTitle>
          <CloseButton onClick={onClose}>×</CloseButton>
        </ModalHeader>

        <ResizableImage
          src={src}
          initialWidth={initialWidth}
          initialHeight={initialHeight}
          onResize={onResize}
          onRemove={onRemove}
        />
      </ModalContent>
    </ModalOverlay>
  );
};

export default function TextEditor({
  value,
  onChange,
  placeholder = 'Start typing...',
  minHeight = 150,
  maxHeight = 400,
  uploadImage
}: TextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imageCounter, setImageCounter] = useState(0);

  // Initialize editor content
  useEffect(() => {
    if (editorRef.current && value !== editorRef.current.innerHTML) {
      editorRef.current.innerHTML = value;
    }
  }, [value]);

  const handleContentChange = useCallback(() => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML;
      onChange(content);
    }
  }, [onChange]);

  const execCommand = useCallback((command: string, value?: string) => {
    try {
      // Check if execCommand is available and use it
      if (typeof document.execCommand === 'function') {
        document.execCommand(command, false, value);
      } else {
        // For browsers that don't support execCommand, handle manually
        console.warn(`execCommand not supported for: ${command}`);
      }
      editorRef.current?.focus();
      handleContentChange();
    } catch (error) {
      console.warn('Error executing command:', command, error);
      editorRef.current?.focus();
    }
  }, [handleContentChange]);

  const isCommandActive = useCallback((command: string) => {
    // Check if we're in the browser environment
    if (typeof window === 'undefined') {
      return false;
    }
    
    try {
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return false;
      
      const range = selection.getRangeAt(0);
      const parentElement = range.commonAncestorContainer.nodeType === Node.TEXT_NODE 
        ? range.commonAncestorContainer.parentElement 
        : range.commonAncestorContainer as Element;
      
      if (!parentElement) return false;
      
      // Check for specific formatting
      switch (command) {
        case 'bold':
          return window.getComputedStyle(parentElement).fontWeight === 'bold' || 
                 window.getComputedStyle(parentElement).fontWeight === '700' ||
                 parentElement.closest('strong, b') !== null;
        case 'italic':
          return window.getComputedStyle(parentElement).fontStyle === 'italic' ||
                 parentElement.closest('em, i') !== null;
        case 'underline':
          return window.getComputedStyle(parentElement).textDecoration.includes('underline') ||
                 parentElement.closest('u') !== null;
        case 'insertUnorderedList':
          return parentElement.closest('ul') !== null;
        case 'insertOrderedList':
          return parentElement.closest('ol') !== null;
        case 'justifyLeft':
          return window.getComputedStyle(parentElement).textAlign === 'left' || 
                 window.getComputedStyle(parentElement).textAlign === 'start';
        case 'justifyCenter':
          return window.getComputedStyle(parentElement).textAlign === 'center';
        case 'justifyRight':
          return window.getComputedStyle(parentElement).textAlign === 'right';
        default:
          return false;
      }
    } catch (error) {
      console.warn('Error checking command state:', error);
      return false;
    }
  }, []);

  // Function to insert a resizable image at cursor position
  const insertResizableImage = useCallback((imageUrl: string) => {
    if (!editorRef.current) return;

    const selection = window.getSelection();
    const range = selection?.getRangeAt(0);

    if (range) {
      // Create a unique ID for this image
      const imageId = `resizable-image-${Date.now()}-${imageCounter}`;
      setImageCounter(prev => prev + 1);

      // Create an img element with resizable attributes
      const img = document.createElement('img');
      img.src = imageUrl;
      img.alt = 'Uploaded image';
      img.setAttribute('data-resizable-image', 'true');
      img.setAttribute('data-image-id', imageId);
      img.style.maxWidth = '300px';
      img.style.height = 'auto';
      img.style.margin = '8px 0';
      img.style.display = 'block';
      img.style.cursor = 'pointer';
      img.style.border = '2px solid transparent';
      img.style.borderRadius = '4px';

      // Add hover effect
      img.addEventListener('mouseenter', () => {
        img.style.borderColor = '#3b82f6';
      });
      img.addEventListener('mouseleave', () => {
        img.style.borderColor = 'transparent';
      });

      // Insert the image
      range.deleteContents();
      range.insertNode(img);

      // Move cursor after the image
      range.setStartAfter(img);
      range.setEndAfter(img);
      selection?.removeAllRanges();
      selection?.addRange(range);

      handleContentChange();
    }
  }, [imageCounter, handleContentChange]);

  const handleImageUpload = useCallback(async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/')) {
      try {
        if (uploadImage) {
          // Upload to server and get URL
          const imageUrl = await uploadImage(file);
          insertResizableImage(imageUrl);
        } else {
          // Fallback to base64
          const reader = new FileReader();
          reader.onload = (e) => {
            const imageUrl = e.target?.result as string;
            insertResizableImage(imageUrl);
          };
          reader.readAsDataURL(file);
        }
      } catch (error) {
        console.error('Failed to upload image:', error);
        // Fallback to base64 on error
        const reader = new FileReader();
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string;
          insertResizableImage(imageUrl);
        };
        reader.readAsDataURL(file);
      }
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [uploadImage, insertResizableImage]);

  // State for managing resizable image modal
  const [selectedImage, setSelectedImage] = useState<{
    element: HTMLImageElement;
    src: string;
    width: number;
    height: number;
  } | null>(null);

  // Handle image click to make it resizable
  const handleImageClick = useCallback((e: Event) => {
    const target = e.target as HTMLImageElement;
    if (target.tagName === 'IMG' && target.getAttribute('data-resizable-image') === 'true') {
      e.preventDefault();
      e.stopPropagation();

      const rect = target.getBoundingClientRect();
      setSelectedImage({
        element: target,
        src: target.src,
        width: target.offsetWidth,
        height: target.offsetHeight
      });
    }
  }, []);

  // Add click listener to editor for image selection
  useEffect(() => {
    const editor = editorRef.current;
    if (editor) {
      editor.addEventListener('click', handleImageClick);
      return () => {
        editor.removeEventListener('click', handleImageClick);
      };
    }
  }, [handleImageClick]);

  // Handle image resize from modal
  const handleImageResize = useCallback((width: number, height: number) => {
    if (selectedImage) {
      selectedImage.element.style.width = `${width}px`;
      selectedImage.element.style.height = `${height}px`;
      selectedImage.element.setAttribute('data-width', width.toString());
      selectedImage.element.setAttribute('data-height', height.toString());
      handleContentChange();
    }
  }, [selectedImage, handleContentChange]);

  // Handle image removal
  const handleImageRemove = useCallback(() => {
    if (selectedImage) {
      selectedImage.element.remove();
      setSelectedImage(null);
      handleContentChange();
    }
  }, [selectedImage, handleContentChange]);

  const toolbarButtons = [
    { command: 'bold', icon: Bold, title: 'Bold' },
    { command: 'italic', icon: Italic, title: 'Italic' },
    { command: 'underline', icon: Underline, title: 'Underline' },
    { separator: true },
    { command: 'insertUnorderedList', icon: List, title: 'Bullet List' },
    { command: 'insertOrderedList', icon: ListOrdered, title: 'Numbered List' },
    { separator: true },
    { command: 'justifyLeft', icon: AlignLeft, title: 'Align Left' },
    { command: 'justifyCenter', icon: AlignCenter, title: 'Align Center' },
    { command: 'justifyRight', icon: AlignRight, title: 'Align Right' },
    { separator: true },
    { command: 'undo', icon: Undo, title: 'Undo' },
    { command: 'redo', icon: Redo, title: 'Redo' },
  ] as const;

  return (
    <EditorContainer>
      <Toolbar>
        {toolbarButtons.map((button, index) => {
          if ('separator' in button && button.separator) {
            return <ToolbarSeparator key={index} />;
          }
          
          if ('icon' in button && button.icon) {
            const Icon = button.icon;
            return (
              <ToolbarButton
                key={button.command}
                type="button"
                title={button.title}
                $active={isCommandActive(button.command)}
                onClick={() => execCommand(button.command)}
              >
                <Icon />
              </ToolbarButton>
            );
          }
          
          return null;
        })}
        
        <ToolbarSeparator />
        
        <ToolbarButton
          type="button"
          title="Insert Image"
          onClick={() => fileInputRef.current?.click()}
        >
          <Image />
        </ToolbarButton>
      </Toolbar>

      <EditorContent
        ref={editorRef}
        contentEditable
        suppressContentEditableWarning
        data-placeholder={placeholder}
        $minHeight={minHeight}
        $maxHeight={maxHeight}
        onInput={handleContentChange}
        onPaste={handleContentChange}
      />

      <ImageUploadInput
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
      />

      {/* Resizable Image Modal */}
      {selectedImage && (
        <ImageResizeModal
          src={selectedImage.src}
          initialWidth={selectedImage.width}
          initialHeight={selectedImage.height}
          onResize={handleImageResize}
          onRemove={handleImageRemove}
          onClose={() => setSelectedImage(null)}
        />
      )}
    </EditorContainer>
  );
}
