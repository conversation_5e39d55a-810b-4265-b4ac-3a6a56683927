'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import styled from 'styled-components';
import { Lock, Unlock, RotateCcw, X } from 'lucide-react';
import { appTheme } from '@/app/theme';

interface ResizableImageProps {
  src: string;
  alt?: string;
  initialWidth?: number;
  initialHeight?: number;
  onResize?: (width: number, height: number) => void;
  onRemove?: () => void;
  className?: string;
}

const ImageContainer = styled.div<{ $width: number; $height: number }>`
  position: relative;
  display: inline-block;
  width: ${props => props.$width}px;
  height: ${props => props.$height}px;
  margin: 8px 0;
  border: 2px solid transparent;
  border-radius: 4px;
  transition: border-color 0.2s ease;

  &:hover {
    border-color: #3b82f6;
  }

  &.selected {
    border-color: #3b82f6;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  }
`;

const StyledImage = styled.img`
  width: 100%;
  height: 100%;
  object-fit: contain;
  border-radius: 2px;
  display: block;
`;

const ResizeHandle = styled.div<{ $position: string }>`
  position: absolute;
  width: 8px;
  height: 8px;
  background: #3b82f6;
  border: 1px solid white;
  border-radius: 2px;
  cursor: ${props => {
    switch (props.$position) {
      case 'nw': return 'nw-resize';
      case 'ne': return 'ne-resize';
      case 'sw': return 'sw-resize';
      case 'se': return 'se-resize';
      case 'n': return 'n-resize';
      case 's': return 's-resize';
      case 'e': return 'e-resize';
      case 'w': return 'w-resize';
      default: return 'default';
    }
  }};
  opacity: 0;
  transition: opacity 0.2s ease;

  ${ImageContainer}:hover & {
    opacity: 1;
  }

  ${props => {
    switch (props.$position) {
      case 'nw': return 'top: -4px; left: -4px;';
      case 'ne': return 'top: -4px; right: -4px;';
      case 'sw': return 'bottom: -4px; left: -4px;';
      case 'se': return 'bottom: -4px; right: -4px;';
      case 'n': return 'top: -4px; left: 50%; transform: translateX(-50%);';
      case 's': return 'bottom: -4px; left: 50%; transform: translateX(-50%);';
      case 'e': return 'right: -4px; top: 50%; transform: translateY(-50%);';
      case 'w': return 'left: -4px; top: 50%; transform: translateY(-50%);';
      default: return '';
    }
  }}
`;

const ControlPanel = styled.div`
  position: absolute;
  top: -40px;
  left: 0;
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(0, 0, 0, 0.8);
  padding: 4px 8px;
  border-radius: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 10;

  ${ImageContainer}:hover & {
    opacity: 1;
  }
`;

const ControlButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: ${props => props.$active ? '#3b82f6' : 'transparent'};
  border: 1px solid ${props => props.$active ? '#3b82f6' : 'rgba(255, 255, 255, 0.3)'};
  border-radius: 2px;
  color: white;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: ${props => props.$active ? '#2563eb' : 'rgba(255, 255, 255, 0.1)'};
    border-color: ${props => props.$active ? '#2563eb' : 'rgba(255, 255, 255, 0.5)'};
  }
`;

const DimensionInput = styled.input`
  width: 50px;
  padding: 2px 4px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.2);
  border-radius: 2px;
  font-size: 11px;
  text-align: center;
  color: #333;

  &:focus {
    outline: none;
    border-color: #3b82f6;
    background: white;
  }
`;

const DimensionLabel = styled.span`
  font-size: 11px;
  color: white;
  margin: 0 2px;
`;

export default function ResizableImage({
  src,
  alt = '',
  initialWidth,
  initialHeight,
  onResize,
  onRemove,
  className
}: ResizableImageProps) {
  const [isSelected, setIsSelected] = useState(false);
  const [aspectRatioLocked, setAspectRatioLocked] = useState(true);
  const [dimensions, setDimensions] = useState(() => {
    if (initialWidth && initialHeight) {
      return { width: initialWidth, height: initialHeight };
    }
    return { width: 300, height: 200 }; // Default dimensions
  });
  const [originalAspectRatio, setOriginalAspectRatio] = useState<number | null>(null);
  const [isResizing, setIsResizing] = useState(false);
  const [resizeStart, setResizeStart] = useState<{
    x: number;
    y: number;
    width: number;
    height: number;
    handle: string;
  } | null>(null);

  const imageRef = useRef<HTMLImageElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Load original image dimensions to calculate aspect ratio
  useEffect(() => {
    const img = new Image();
    img.onload = () => {
      const aspectRatio = img.naturalWidth / img.naturalHeight;
      setOriginalAspectRatio(aspectRatio);
      
      // If no initial dimensions provided, use natural dimensions (scaled down if too large)
      if (!initialWidth && !initialHeight) {
        const maxWidth = 600;
        const maxHeight = 400;
        
        let newWidth = img.naturalWidth;
        let newHeight = img.naturalHeight;
        
        if (newWidth > maxWidth) {
          newHeight = (newHeight * maxWidth) / newWidth;
          newWidth = maxWidth;
        }
        
        if (newHeight > maxHeight) {
          newWidth = (newWidth * maxHeight) / newHeight;
          newHeight = maxHeight;
        }
        
        setDimensions({ width: Math.round(newWidth), height: Math.round(newHeight) });
      }
    };
    img.src = src;
  }, [src, initialWidth, initialHeight]);

  // Handle resize start
  const handleResizeStart = useCallback((e: React.MouseEvent, handle: string) => {
    e.preventDefault();
    e.stopPropagation();
    
    setIsResizing(true);
    setResizeStart({
      x: e.clientX,
      y: e.clientY,
      width: dimensions.width,
      height: dimensions.height,
      handle
    });
  }, [dimensions]);

  // Handle resize move
  const handleResizeMove = useCallback((e: MouseEvent) => {
    if (!isResizing || !resizeStart || !originalAspectRatio) return;

    const deltaX = e.clientX - resizeStart.x;
    const deltaY = e.clientY - resizeStart.y;
    
    let newWidth = resizeStart.width;
    let newHeight = resizeStart.height;

    // Calculate new dimensions based on handle position
    switch (resizeStart.handle) {
      case 'se': // Southeast
        newWidth = Math.max(50, resizeStart.width + deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        } else {
          newHeight = Math.max(50, resizeStart.height + deltaY);
        }
        break;
      case 'sw': // Southwest
        newWidth = Math.max(50, resizeStart.width - deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        } else {
          newHeight = Math.max(50, resizeStart.height + deltaY);
        }
        break;
      case 'ne': // Northeast
        newWidth = Math.max(50, resizeStart.width + deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        } else {
          newHeight = Math.max(50, resizeStart.height - deltaY);
        }
        break;
      case 'nw': // Northwest
        newWidth = Math.max(50, resizeStart.width - deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        } else {
          newHeight = Math.max(50, resizeStart.height - deltaY);
        }
        break;
      case 'e': // East
        newWidth = Math.max(50, resizeStart.width + deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        }
        break;
      case 'w': // West
        newWidth = Math.max(50, resizeStart.width - deltaX);
        if (aspectRatioLocked) {
          newHeight = newWidth / originalAspectRatio;
        }
        break;
      case 'n': // North
        newHeight = Math.max(50, resizeStart.height - deltaY);
        if (aspectRatioLocked) {
          newWidth = newHeight * originalAspectRatio;
        }
        break;
      case 's': // South
        newHeight = Math.max(50, resizeStart.height + deltaY);
        if (aspectRatioLocked) {
          newWidth = newHeight * originalAspectRatio;
        }
        break;
    }

    const roundedDimensions = {
      width: Math.round(newWidth),
      height: Math.round(newHeight)
    };

    setDimensions(roundedDimensions);
    onResize?.(roundedDimensions.width, roundedDimensions.height);
  }, [isResizing, resizeStart, originalAspectRatio, aspectRatioLocked, onResize]);

  // Handle resize end
  const handleResizeEnd = useCallback(() => {
    setIsResizing(false);
    setResizeStart(null);
  }, []);

  // Add global mouse event listeners for resizing
  useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleResizeMove);
      document.addEventListener('mouseup', handleResizeEnd);
      
      return () => {
        document.removeEventListener('mousemove', handleResizeMove);
        document.removeEventListener('mouseup', handleResizeEnd);
      };
    }
  }, [isResizing, handleResizeMove, handleResizeEnd]);

  // Handle dimension input changes
  const handleWidthChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newWidth = Math.max(50, parseInt(e.target.value) || 50);
    let newHeight = dimensions.height;
    
    if (aspectRatioLocked && originalAspectRatio) {
      newHeight = Math.round(newWidth / originalAspectRatio);
    }
    
    const newDimensions = { width: newWidth, height: newHeight };
    setDimensions(newDimensions);
    onResize?.(newDimensions.width, newDimensions.height);
  }, [dimensions.height, aspectRatioLocked, originalAspectRatio, onResize]);

  const handleHeightChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newHeight = Math.max(50, parseInt(e.target.value) || 50);
    let newWidth = dimensions.width;
    
    if (aspectRatioLocked && originalAspectRatio) {
      newWidth = Math.round(newHeight * originalAspectRatio);
    }
    
    const newDimensions = { width: newWidth, height: newHeight };
    setDimensions(newDimensions);
    onResize?.(newDimensions.width, newDimensions.height);
  }, [dimensions.width, aspectRatioLocked, originalAspectRatio, onResize]);

  // Reset to original dimensions
  const handleReset = useCallback(() => {
    if (originalAspectRatio) {
      const img = new Image();
      img.onload = () => {
        const maxWidth = 600;
        const maxHeight = 400;
        
        let newWidth = img.naturalWidth;
        let newHeight = img.naturalHeight;
        
        if (newWidth > maxWidth) {
          newHeight = (newHeight * maxWidth) / newWidth;
          newWidth = maxWidth;
        }
        
        if (newHeight > maxHeight) {
          newWidth = (newWidth * maxHeight) / newHeight;
          newHeight = maxHeight;
        }
        
        const newDimensions = { width: Math.round(newWidth), height: Math.round(newHeight) };
        setDimensions(newDimensions);
        onResize?.(newDimensions.width, newDimensions.height);
      };
      img.src = src;
    }
  }, [originalAspectRatio, src, onResize]);

  return (
    <ImageContainer
      ref={containerRef}
      $width={dimensions.width}
      $height={dimensions.height}
      className={`${className || ''} ${isSelected ? 'selected' : ''}`}
      onClick={() => setIsSelected(true)}
      onBlur={() => setIsSelected(false)}
      tabIndex={0}
    >
      <StyledImage
        ref={imageRef}
        src={src}
        alt={alt}
        draggable={false}
      />
      
      {/* Resize handles */}
      {['nw', 'ne', 'sw', 'se', 'n', 's', 'e', 'w'].map(handle => (
        <ResizeHandle
          key={handle}
          $position={handle}
          onMouseDown={(e) => handleResizeStart(e, handle)}
        />
      ))}
      
      {/* Control panel */}
      <ControlPanel>
        <DimensionInput
          type="number"
          value={dimensions.width}
          onChange={handleWidthChange}
          min="50"
        />
        <DimensionLabel>×</DimensionLabel>
        <DimensionInput
          type="number"
          value={dimensions.height}
          onChange={handleHeightChange}
          min="50"
        />
        
        <ControlButton
          $active={aspectRatioLocked}
          onClick={() => setAspectRatioLocked(!aspectRatioLocked)}
          title={aspectRatioLocked ? 'Unlock aspect ratio' : 'Lock aspect ratio'}
        >
          {aspectRatioLocked ? <Lock size={12} /> : <Unlock size={12} />}
        </ControlButton>
        
        <ControlButton
          onClick={handleReset}
          title="Reset to original size"
        >
          <RotateCcw size={12} />
        </ControlButton>
        
        {onRemove && (
          <ControlButton
            onClick={onRemove}
            title="Remove image"
          >
            <X size={12} />
          </ControlButton>
        )}
      </ControlPanel>
    </ImageContainer>
  );
}
